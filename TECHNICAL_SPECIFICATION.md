# Technical Specification Document
## Huitong Material 3D Rendering Web Application

### Executive Summary
This is a sophisticated 3D material visualization web application built with React, Three.js, and modern web technologies. The application allows users to upload, view, and apply materials to 3D models in real-time, featuring an admin dashboard for content management.

---

## 1. Architecture Overview

### 1.1 System Architecture
```
Frontend (React + Vite) ←→ Backend (Express.js) ←→ Database (PostgreSQL)
                                    ↓
                            File Storage (Vercel Blob)
```

### 1.2 Component Relationships
- **Frontend**: React SPA with TypeScript, routing via React Router
- **Backend**: Express.js API server with Prisma ORM
- **Database**: PostgreSQL with Prisma schema management
- **Storage**: Vercel Blob for file uploads (models, thumbnails)
- **Deployment**: Vercel serverless functions + static hosting

---

## 2. 3D Rendering Technology Stack

### 2.1 Core 3D Libraries
- **Three.js v0.177.0**: Core 3D rendering engine
- **@react-three/fiber v9.1.2**: React renderer for Three.js
- **@react-three/drei v10.1.2**: Useful helpers and abstractions

### 2.2 3D Features
- **Model Support**: GLB/GLTF format support
- **Material System**: PBR materials with metalness, roughness, transparency
- **Lighting**: Environment-based lighting with HDR environments
- **Controls**: Orbit controls for camera manipulation
- **Performance**: Texture caching and optimized rendering

### 2.3 Material Properties
```typescript
interface MaterialProps {
  color: string;        // Hex color
  metalness: number;    // 0-1 range
  roughness: number;    // 0-1 range
  opacity: number;      // 0-1 range (transparency)
}
```

---

## 3. Frontend Framework and Build Tools

### 3.1 Core Framework
- **React v19.1.0**: Latest React with concurrent features
- **TypeScript v5.8.3**: Strong typing throughout
- **Vite v6.3.5**: Fast build tool and dev server

### 3.2 Routing and State
- **React Router DOM v7.6.2**: Client-side routing
- **Local State**: useState, useRef for component state
- **Context**: Notification system with React Context

### 3.3 UI Components
- **Lucide React v0.513.0**: Icon library
- **React Colorful v5.6.1**: Color picker component
- **Custom Components**: Modular component architecture

### 3.4 Build Configuration
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: { '/api': 'http://localhost:3001' }
  }
})
```

---

## 4. Database Schema and Data Flow

### 4.1 Database Models
```prisma
model Model {
  id        String   @id @default(uuid())
  name      String
  thumbnail String?
  fileType  String?
  size      String?
  createdAt DateTime @default(now())
  filePath  String
  url       String   @default("")
}

model Material {
  id        String   @id @default(uuid())
  name      String
  thumbnail String?
  color     String
  metalness Float    @default(0)
  roughness Float    @default(0.5)
  glass     Float    @default(0)
  createdAt DateTime @default(now())
}
```

### 4.2 Data Flow
1. **Upload**: Client → Vercel Blob → Database record
2. **Retrieval**: Database → API → Frontend transformation
3. **Rendering**: Frontend → Three.js scene graph

### 4.3 API Endpoints
- `GET /api/models` - List all models
- `POST /api/models` - Create new model
- `DELETE /api/models/:id` - Delete model
- `GET /api/materials` - List all materials
- `POST /api/materials` - Create new material
- `DELETE /api/materials/:id` - Delete material
- `POST /api/upload` - Handle file uploads

---

## 5. Storage Patterns and File Management

### 5.1 File Storage Strategy
- **Provider**: Vercel Blob Storage
- **File Types**: GLB/GLTF models, PNG/JPG thumbnails
- **Upload Flow**: Client-side direct upload with signed URLs
- **Cleanup**: Automatic deletion when records are removed

### 5.2 File Organization
```
/uploads/
  ├── models/     (Legacy - not used with Vercel Blob)
  ├── materials/  (Legacy - not used with Vercel Blob)
  └── thumbnails/ (Legacy - not used with Vercel Blob)
```

### 5.3 Upload Implementation
```typescript
// Client-side upload with progress tracking
const uploadFile = async (file: File, onProgress?: (progress: number) => void) => {
  const blob = await upload(file.name, file, {
    access: 'public',
    handleUploadUrl: '/api/upload',
    onUploadProgress: ({ progress }) => onProgress?.(progress)
  });
  return blob.url;
};
```

---

## 6. Deployment Configuration and Environment

### 6.1 Vercel Configuration
```json
{
  "version": 2,
  "builds": [
    { "src": "api/**/*.mjs", "use": "@vercel/node" },
    { "src": "package.json", "use": "@vercel/static-build" }
  ],
  "rewrites": [
    { "source": "/api/(.*)", "destination": "/api/index.mjs" },
    { "source": "/(.*)", "destination": "/index.html" }
  ]
}
```

### 6.2 Environment Variables
```bash
# Database
POSTGRES_PRISMA_URL=          # Connection pooling URL
POSTGRES_URL_NON_POOLING=     # Direct connection URL

# Vercel Blob
BLOB_READ_WRITE_TOKEN=        # Auto-set by Vercel

# Development
NODE_ENV=development
PORT=3001
```

### 6.3 Dual Deployment Strategy
- **Vercel**: Serverless functions + static hosting
- **Local/Alibaba Cloud**: Traditional server deployment
- **Shared Codebase**: Same backend logic for both environments

---

## 7. Security and Performance

### 7.1 Security Measures
- **Input Validation**: Server-side validation for all inputs
- **File Upload**: Signed URLs prevent unauthorized uploads
- **Authentication**: Simple localStorage-based admin auth
- **CORS**: Configured for cross-origin requests

### 7.2 Performance Optimizations
- **Texture Caching**: Global texture cache for 3D materials
- **Lazy Loading**: Suspense boundaries for 3D components
- **Build Optimization**: Vite's optimized bundling
- **Database**: Indexed queries with Prisma

---

## 8. Development Workflow

### 8.1 Scripts
```json
{
  "dev": "concurrently frontend and backend",
  "build": "prisma generate && vite build",
  "lint": "eslint with TypeScript support",
  "lint:unused": "ts-prune for dead code detection"
}
```

### 8.2 Code Quality
- **ESLint**: TypeScript-aware linting
- **Husky**: Git hooks for pre-commit checks
- **Lint-staged**: Staged file linting
- **TypeScript**: Strict mode enabled

### 8.3 Development Tools
- **Nodemon**: Backend auto-restart
- **Concurrently**: Parallel dev server execution
- **Prisma Studio**: Database GUI
- **Vite HMR**: Hot module replacement
