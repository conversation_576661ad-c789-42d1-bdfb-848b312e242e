import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

import express from 'express';
import cors from 'cors';
import { PrismaClient } from '@prisma/client';
import { del as vercelBlobDel } from '@vercel/blob';
import { handleUpload } from '@vercel/blob/client';

const app = express();
const prisma = new PrismaClient();

app.use(cors());
app.use(express.json());

// --- API Routes ---

// This new endpoint will handle generating a signed URL for client-side uploads.
app.post('/api/upload', async (req, res) => {
  try {
    const jsonResponse = await handleUpload({
      body: req.body,
      request: req,
      onBeforeGenerateToken: async (pathname) => {
        // The token is read from the BLOB_READ_WRITE_TOKEN environment variable that
        // is automatically set by Vercel when a Blob store is connected.
        return {
          // Can add metadata to the token payload, like a user ID
        };
      },
      onUploadCompleted: async ({ blob, tokenPayload }) => {
        // This callback is executed after the file is uploaded to Vercel Blob.
        console.log('Blob upload completed!', blob, tokenPayload);
      },
    });

    res.status(200).json(jsonResponse);
  } catch (error) {
    console.error('An error occurred during upload handling:', error);
    res.status(400).json({ error: error.message });
  }
});

// GET all models
app.get('/api/models', async (req, res) => {
    try {
        const models = await prisma.model.findMany({
            orderBy: { createdAt: 'desc' },
        });
        res.json(models);
    } catch (error) {
        console.error('Failed to fetch models:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// GET a single model by ID
app.get('/api/models/:id', async (req, res) => {
    try {
        const model = await prisma.model.findUnique({ where: { id: req.params.id } });
        if (!model) return res.status(404).json({ error: 'Model not found' });
        res.json(model);
    } catch (error) {
        console.error(`Failed to fetch model ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Input validation helper
const validateModelInput = (data) => {
    const { name, url } = data;
    const errors = [];

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
        errors.push('Model name is required and must be a non-empty string');
    }

    if (!url || typeof url !== 'string' || !url.startsWith('http')) {
        errors.push('Valid model URL is required');
    }

    if (name && name.length > 255) {
        errors.push('Model name must be less than 255 characters');
    }

    return errors;
};

// POST a new model - now expects URLs from client-side upload
app.post('/api/models', async (req, res) => {
    const { name, fileType, size, url, thumbnailUrl } = req.body;

    // Validate input
    const validationErrors = validateModelInput({ name, url });
    if (validationErrors.length > 0) {
        return res.status(400).json({
            error: 'Validation failed',
            details: validationErrors
        });
    }

    try {
        const newModel = await prisma.model.create({
            data: {
                name: name.trim(),
                fileType: fileType || 'GLB',
                size: size ? String(size) : '0',
                filePath: url,
                url: url,
                thumbnail: thumbnailUrl || null,
            },
        });
        res.status(201).json(newModel);
    } catch (error) {
        console.error('Failed to create model record:', error);
        res.status(500).json({ error: 'Failed to create model record' });
    }
});

// PUT update a model
app.put('/api/models/:id', async (req, res) => {
    const { name, thumbnailUrl } = req.body;

    try {
        const existingModel = await prisma.model.findUnique({ where: { id: req.params.id } });
        if (!existingModel) return res.status(404).json({ error: 'Model not found' });

        const updatedModel = await prisma.model.update({
            where: { id: req.params.id },
            data: {
                name: name || existingModel.name,
                thumbnail: thumbnailUrl !== undefined ? thumbnailUrl : existingModel.thumbnail,
            },
        });
        res.json(updatedModel);
    } catch (error) {
        console.error(`Failed to update model ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// DELETE a model
app.delete('/api/models/:id', async (req, res) => {
    try {
        const model = await prisma.model.findUnique({ where: { id: req.params.id } });
        if (!model) return res.status(404).json({ error: 'Model not found' });

        // Delete files from Vercel Blob storage
        if (model.url) await vercelBlobDel(model.url);
        if (model.thumbnail) await vercelBlobDel(model.thumbnail);

        // Delete from database
        await prisma.model.delete({ where: { id: req.params.id } });

        res.status(204).send();
    } catch (error) {
        console.error(`Failed to delete model ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});


// GET all materials
app.get('/api/materials', async (req, res) => {
    try {
        const materials = await prisma.material.findMany({
            orderBy: { createdAt: 'desc' },
        });
        res.json(materials);
    } catch (error) {
        console.error('Failed to fetch materials:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Input validation helper for materials
const validateMaterialInput = (data) => {
    const { name, color, metalness, roughness, glass } = data;
    const errors = [];

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
        errors.push('Material name is required and must be a non-empty string');
    }

    if (!color || typeof color !== 'string' || !/^#[0-9A-Fa-f]{6}$/.test(color)) {
        errors.push('Valid hex color is required (e.g., #FF0000)');
    }

    if (name && name.length > 255) {
        errors.push('Material name must be less than 255 characters');
    }

    const numericFields = { metalness, roughness, glass };
    for (const [field, value] of Object.entries(numericFields)) {
        if (value !== undefined && value !== null) {
            const num = parseFloat(value);
            if (isNaN(num) || num < 0 || num > 1) {
                errors.push(`${field} must be a number between 0 and 1`);
            }
        }
    }

    return errors;
};

// POST a new material - now expects thumbnail URL from client-side upload
app.post('/api/materials', async (req, res) => {
    const { name, color, metalness, roughness, glass, thumbnailUrl } = req.body;

    // Validate input
    const validationErrors = validateMaterialInput({ name, color, metalness, roughness, glass });
    if (validationErrors.length > 0) {
        return res.status(400).json({
            error: 'Validation failed',
            details: validationErrors
        });
    }

    try {
        const newMaterial = await prisma.material.create({
            data: {
                name: name.trim(),
                color,
                metalness: parseFloat(metalness) || 0,
                roughness: parseFloat(roughness) || 0.5,
                glass: parseFloat(glass) || 0,
                thumbnail: thumbnailUrl || null,
            },
        });
        res.status(201).json(newMaterial);
    } catch (error) {
        console.error('Failed to add material:', error);
        res.status(500).json({ error: 'Failed to create material record' });
    }
});

// DELETE a material
app.delete('/api/materials/:id', async (req, res) => {
    try {
        const material = await prisma.material.findUnique({ where: { id: req.params.id } });
        if (material && material.thumbnail) {
            await vercelBlobDel(material.thumbnail);
        }
        await prisma.material.delete({ where: { id: req.params.id } });
        res.status(204).send();
    } catch (error) {
        console.error(`Failed to delete material ${req.params.id}:`, error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// --- Export and Listen ---

// This allows Vercel to import the app as a serverless function.
export default app;

// This block runs only when not in a Vercel environment (i.e., for Aliyun or local dev)
// This block runs for local development or any non-Vercel production environment.
// When running with `npm run dev`, NODE_ENV is 'development', ensuring the server starts.
if (process.env.NODE_ENV === 'development' || !process.env.VERCEL) {
    const PORT = process.env.PORT || 3001;
    app.listen(PORT, () => {
        console.log(`Server is running for production on http://localhost:${PORT}`);
    });
}
